import { WorldMapCell } from '../../shared/types/World';
import { LocationConfig, locationConfigs } from './constants/locationConfig';
import { Point, LocationInteractive } from '../../shared/types/Location';
import { Door } from '../../shared/types/Door';
import { LocationType, LocationDecorations, LocationSubtype } from '../../shared/enums';
import {
	OUTDOOR_SPAWN_MARGIN,
	OUTDOOR_GOBACK_ZONE_WIDTH,
	NATURE_DECORATIONS,
	DEBRIS_DECORATIONS,
	INDOOR_DOORS_COUNT
} from './constants/locationInteriorConstants';
import { MaterialTexture } from '../../shared/enums';

export async function generateGridForLocation(
	cell: WorldMapCell,
	config: LocationConfig,
	rng: () => number
): Promise<void> {
	const location = cell.location!;
	
	// Определение размера локации на основе конфигурации
	const [minSize, maxSize] = config.size;
	const width = Math.floor(rng() * (maxSize - minSize + 1)) + minSize;
	const height = Math.floor(rng() * (maxSize - minSize + 1)) + minSize;
	
	// Устанавливаем размер локации
	location.locationSize = [width, height];
	
	// Устанавливаем тип локации из конфига
	location.type = config.type;
	
	// Инициализируем массивы декораций и интерактивных элементов
	if (!location.decorations) {
		location.decorations = {};
	}
	if (!location.interactive) {
		location.interactive = [];
	}
	if (config.type === LocationType.OUTDOOR) {
		await generateOutdoorLocation(location, width, height, rng);
	} else {
		await generateIndoorUndergroundLocation(location, width, height, config.type, rng);
	}
	
	// Устанавливаем материал локации (пост-обработка)
	setLocationMaterial(location, rng);
}

async function generateOutdoorLocation(
	location: any,
	width: number,
	height: number,
	rng: () => number
): Promise<void> {
	// Генерируем природные декорации: трава, кусты, деревья
	await generateNatureDecorations(location, width, height, rng);
	
	// Генерируем мусор и покрышки
	await generateDebrisDecorations(location, width, height, rng);
	
	// Устанавливаем спавн в одной из 4 полос (отступ 5-7 клеток от края)
	const minMargin = 5;
	const maxMargin = 6;
	
	// Выбираем случайную полосу (0-север, 1-восток, 2-юг, 3-запад)
	const stripIndex = Math.floor(rng() * 4);
	let spawnX: number, spawnY: number;
	
	switch (stripIndex) {
		case 0: // Северная полоса
			spawnX = Math.floor(rng() * width);
			spawnY = minMargin + Math.floor(rng() * (Math.min(maxMargin, height - 1) - minMargin + 1));
			break;
		case 1: // Восточная полоса
			spawnX = width - 1 - (minMargin + Math.floor(rng() * (Math.min(maxMargin, width - 1) - minMargin + 1)));
			spawnY = Math.floor(rng() * height);
			break;
		case 2: // Южная полоса
			spawnX = Math.floor(rng() * width);
			spawnY = height - 1 - (minMargin + Math.floor(rng() * (Math.min(maxMargin, height - 1) - minMargin + 1)));
			break;
		case 3: // Западная полоса
			spawnX = minMargin + Math.floor(rng() * (Math.min(maxMargin, width - 1) - minMargin + 1));
			spawnY = Math.floor(rng() * height);
			break;
		default:
			// Fallback на центр
			spawnX = Math.floor(width / 2);
			spawnY = Math.floor(height / 2);
	}
	
	location.spawnPosition = [spawnX, spawnY];
	location.playerPosition = [spawnX, spawnY];
	
	// goBackZone - 3 полосы вдоль каждой границы карты
	location.goBackPosition = [];
	for (let x = 0; x < width; x++) {
		for (let y = 0; y < height; y++) {
			const isInGoBackZone = x < OUTDOOR_GOBACK_ZONE_WIDTH || 
								  x >= width - OUTDOOR_GOBACK_ZONE_WIDTH || 
								  y < OUTDOOR_GOBACK_ZONE_WIDTH || 
								  y >= height - OUTDOOR_GOBACK_ZONE_WIDTH;
			if (isInGoBackZone) {
				location.goBackPosition.push([x, y]);
			}
		}
	}
}

async function generateIndoorUndergroundLocation(
	location: any,
	width: number,
	height: number,
	locationType: LocationType,
	rng: () => number
): Promise<void> {
	// Создаем стены по периметру
	const walls: Point[] = [];
	for (let x = 0; x < width; x++) {
		for (let y = 0; y < height; y++) {
			const isPerimeter = x === 0 || x === width - 1 || y === 0 || y === height - 1;
			if (isPerimeter) {
				walls.push([x, y]);
			}
		}
	}
	
	if (!location.decorations[LocationDecorations.WALL]) {
		location.decorations[LocationDecorations.WALL] = [];
	}
	location.decorations[LocationDecorations.WALL] = walls;
	
	// Выбираем 2 случайные стороны для дверей
	const sides = ['north', 'south', 'east', 'west'];
	const selectedSides = [];
	for (let i = 0; i < INDOOR_DOORS_COUNT; i++) {
		const randomIndex = Math.floor(rng() * sides.length);
		selectedSides.push(sides[randomIndex]);
		sides.splice(randomIndex, 1);
	}
	
	// Создаем двери на выбранных сторонах
	const doors: Point[] = [];
	const doorPositions: Point[] = [];
	
	for (const side of selectedSides) {
		let doorX: number, doorY: number;
		
		switch (side) {
			case 'north':
				doorX = Math.floor(width / 2);
				doorY = 0;
				break;
			case 'south':
				doorX = Math.floor(width / 2);
				doorY = height - 1;
				break;
			case 'east':
				doorX = width - 1;
				doorY = Math.floor(height / 2);
				break;
			case 'west':
				doorX = 0;
				doorY = Math.floor(height / 2);
				break;
			default:
				continue;
		}
		
		doors.push([doorX, doorY]);
		doorPositions.push([doorX, doorY]);
		
		// Убираем стену в месте двери
		const wallIndex = location.decorations[LocationDecorations.WALL].findIndex(
			([x, y]: Point) => x === doorX && y === doorY
		);
		if (wallIndex !== -1) {
			location.decorations[LocationDecorations.WALL].splice(wallIndex, 1);
		}
		
		// Добавляем интерактивную дверь
		// Создаем объект Door и присваиваем его в поле type (совместимо с LocationInteractive.type)
		const doorObj: Door = {
			position: [doorX, doorY],
			material: MaterialTexture.BETON,
			direction: side === 'north' ? 1 : side === 'east' ? 2 : side === 'south' ? 3 : 4,
			isOpen: false,
			blocked: false
		};

		const doorInteractive: LocationInteractive = {
			id: `door_${doorX}_${doorY}`,
			type: doorObj,
			name: 'Дверь',
			description: 'Выход из локации',
			position: { x: doorX, y: doorY }
		};
		location.interactive.push(doorInteractive);
	}
	
	// goBackPosition - позиции дверей
	location.goBackPosition = doorPositions;
	
	// Спавн - соседняя клетка внутрь от двери (первой)
	if (doorPositions.length > 0) {
		const [doorX, doorY] = doorPositions[0];
		let spawnX = doorX, spawnY = doorY;
		
		// Сдвигаем спавн внутрь от двери
		if (doorX === 0) spawnX = 1;
		else if (doorX === width - 1) spawnX = width - 2;
		else if (doorY === 0) spawnY = 1;
		else if (doorY === height - 1) spawnY = height - 2;
		
		location.spawnPosition = [spawnX, spawnY];
		location.playerPosition = [spawnX, spawnY];
	}
	
	// Генерируем информацию о сторонах для стен и дверей
	generateDecorationSides(location, width, height);
}

// Универсальная функция для установки материала локации
function setLocationMaterial(location: any, rng: () => number): void {
	const materialValues = Object.values(MaterialTexture) as string[];
	let allowedMaterials: string[];
	
	// Список подтипов, для которых нельзя использовать WOOD
	const noWoodSubtypes = [
		LocationSubtype.MILITARY,
		LocationSubtype.BUNKER,
		LocationSubtype.GASSTATION,
		LocationSubtype.SCHOOL,
		LocationSubtype.HOSPITAL,
		LocationSubtype.SUBWAY,
		LocationSubtype.POLICE,
		LocationSubtype.LABORATORY
	];
	const noBrickSubtypes = [
		LocationSubtype.BUNKER,
		LocationSubtype.SUBWAY
	];

	if (location.subType && noWoodSubtypes.includes(location.subType)) {
		if (location.subType && noBrickSubtypes.includes(location.subType)) {
			// Для определённых подтипов запрещаем BRICK и WOOD
			allowedMaterials = allowedMaterials.filter(m => m !== MaterialTexture.BRICK && m !== MaterialTexture.WOOD);
		};
		// Для определённых подтипов запрещаем WOOD
		allowedMaterials = materialValues.filter(m => m !== MaterialTexture.WOOD)
		
	} else if (location.type === LocationType.OUTDOOR || location.type === LocationType.BEACH) {
		// Для OUTDOOR и BEACH разрешены все материалы
		allowedMaterials = materialValues;
	} else {
		// Для остальных INDOOR/UNDERGROUND запрещаем WOOD
		allowedMaterials = materialValues.filter(m => m !== MaterialTexture.WOOD);
	}
	
	location.textureMaterial = allowedMaterials[Math.floor(rng() * allowedMaterials.length)];
}

// Функция для определения сторон стен и дверей
function generateDecorationSides(location: any, width: number, height: number): void {
	if (!location.decorationSide) {
		location.decorationSide = [];
	}
	
	// Добавляем стороны для стен
	if (location.decorations[LocationDecorations.WALL]) {
		for (const [x, y] of location.decorations[LocationDecorations.WALL]) {
			const sides = getWallSides(x, y, width, height);
			location.decorationSide.push([x, y, sides]);
		}
	}
	
	// Добавляем стороны для дверей (интерактивных объектов).
	// Поддерживаем два варианта: когда interactive.type === 'interactive' (строка)
	// и когда interactive.type === Door (объект с полем 'direction').
	if (location.interactive) {
		for (const inter of location.interactive) {
			const isNamedDoor = inter.name === 'Дверь';
			const typeIsStringInteractive = inter.type === 'interactive';
			const typeIsDoorObject = inter.type && typeof inter.type === 'object' && ('direction' in inter.type || 'isOpen' in inter.type);

			if (!isNamedDoor) continue;

			if (typeIsDoorObject) {
				// Если это объект Door, используем его direction (1..4)
				const dir = (inter.type as any).direction;
				const sides = Array.isArray(dir) ? dir : [dir];
				location.decorationSide.push([inter.position.x, inter.position.y, sides]);
			} else if (typeIsStringInteractive) {
				// Старый вариант: строковый тип 'interactive' — определяем по позиции
				const sides = getDoorSides(inter.position.x, inter.position.y, width, height);
				location.decorationSide.push([inter.position.x, inter.position.y, sides]);
			}
		}
	}
}

// Определяет стороны стены: 1=север, 2=восток, 3=юг, 4=запад
function getWallSides(x: number, y: number, width: number, height: number): number[] {
	const sides: number[] = [];
	
	// Угловые стены
	if (x === 0 && y === 0) {
		// Левый верхний угол
		sides.push(1, 4); // север, запад
	} else if (x === width - 1 && y === 0) {
		// Правый верхний угол  
		sides.push(1, 2); // север, восток
	} else if (x === 0 && y === height - 1) {
		// Левый нижний угол
		sides.push(3, 4); // юг, запад
	} else if (x === width - 1 && y === height - 1) {
		// Правый нижний угол
		sides.push(2, 3); // восток, юг
	} 
	// Стены по сторонам
	else if (y === 0) {
		// Северная стена
		sides.push(1); // север
	} else if (x === width - 1) {
		// Восточная стена
		sides.push(2); // восток
	} else if (y === height - 1) {
		// Южная стена
		sides.push(3); // юг
	} else if (x === 0) {
		// Западная стена
		sides.push(4); // запад
	}
	
	return sides;
}

// Определяет сторону двери
function getDoorSides(x: number, y: number, width: number, height: number): number[] {
	const sides: number[] = [];
	
	if (y === 0) {
		sides.push(1); // север
	} else if (x === width - 1) {
		sides.push(2); // восток
	} else if (y === height - 1) {
		sides.push(3); // юг
	} else if (x === 0) {
		sides.push(4); // запад
	}
	
	return sides;
}

// Универсальная функция для генерации декораций
function generateDecorationPositions(
	totalCells: number,
	width: number,
	height: number,
	minPercent: number,
	maxPercent: number,
	rng: () => number
): Point[] {
	const percent = minPercent + rng() * (maxPercent - minPercent);
	const count = Math.floor(totalCells * percent);
	const positions: Point[] = [];
	
	for (let i = 0; i < count; i++) {
		const x = Math.floor(rng() * width);
		const y = Math.floor(rng() * height);
		positions.push([x, y]);
	}
	
	return positions;
}

async function generateNatureDecorations(
	location: any,
	width: number,
	height: number,
	rng: () => number
): Promise<void> {
	const totalCells = width * height;
	
	// Генерируем все природные декорации одним циклом
	const decorationTypes = [
		{ type: LocationDecorations.GRASS, config: NATURE_DECORATIONS.GRASS },
		{ type: LocationDecorations.BUSH, config: NATURE_DECORATIONS.BUSH },
		{ type: LocationDecorations.TREE, config: NATURE_DECORATIONS.TREE },
		{ type: LocationDecorations.ROCKS, config: NATURE_DECORATIONS.ROCKS },
		{ type: LocationDecorations.PUDDLE, config: NATURE_DECORATIONS.PUDDLE },
		{ type: LocationDecorations.LOG, config: NATURE_DECORATIONS.LOG },
		{ type: LocationDecorations.MUD, config: NATURE_DECORATIONS.MUD }
	];
	
	for (const { type, config } of decorationTypes) {
		location.decorations[type] = generateDecorationPositions(
			totalCells, width, height, config.min, config.max, rng
		);
	}
}

async function generateDebrisDecorations(
	location: any,
	width: number,
	height: number,
	rng: () => number
): Promise<void> {
	const totalCells = width * height;
	
	// Генерируем все мусорные декорации одним циклом
	const decorationTypes = [
		{ type: LocationDecorations.LITTER, config: DEBRIS_DECORATIONS.LITTER },
		{ type: LocationDecorations.TIRE, config: DEBRIS_DECORATIONS.TIRE }
	];
	
	for (const { type, config } of decorationTypes) {
		location.decorations[type] = generateDecorationPositions(
			totalCells, width, height, config.min, config.max, rng
		);
	}
}
