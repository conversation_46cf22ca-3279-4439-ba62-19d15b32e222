import { TransferLocation } from "../../../../shared/types/Location";
import { LocationType, LocationSubtype, TerrainType } from "../../../../shared/enums";
import { createBuildingPlan, addBuildingDoorsFromPlan, createBuildingWallsWithDoors } from "./doors";

/**
 * Тестирует новую систему дверей
 */
export function testNewDoorSystem(): void {
  console.log("=== Тестирование новой системы дверей ===");

  // Создаем тестовую локацию
  const location: TransferLocation = {
    id: "test",
    name: "Test Location",
    description: "Test",
    locationSize: [50, 50],
    type: LocationType.INDOOR,
    subtype: LocationSubtype.SHOP,
    morality: 10,
    terrain: TerrainType.GROUND,
    playerPresent: false,
    playerPosition: [25, 25],
    spawnPosition: [25, 25],
    goBackPosition: [[25, 25]],
    isDiscovered: false,
    isVisible: true,
    decorations: {},
    decorationZoneType: {}
  };

  // Простой RNG для тестирования
  let seed = 12345;
  const rng = () => {
    seed = (seed * 9301 + 49297) % 233280;
    return seed / 233280;
  };

  // Тест 1: Маленькое здание (одна комната)
  console.log("\n--- Тест 1: Маленькое здание 6x6 ---");
  testBuildingSize(location, 10, 10, 6, 1, rng);

  // Тест 2: Среднее здание (2-3 комнаты)
  console.log("\n--- Тест 2: Среднее здание 10x10 ---");
  testBuildingSize(location, 15, 15, 10, 3, rng);

  // Тест 3: Большое здание (4-5 комнат)
  console.log("\n--- Тест 3: Большое здание 15x15 ---");
  testBuildingSize(location, 20, 20, 15, 5, rng);

  console.log("\n=== Тестирование завершено ===");
}

function testBuildingSize(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number,
  roomCount: number,
  rng: () => number
): void {
  // Очищаем локацию
  location.decorations = {};
  location.decorationZoneType = {};

  try {
    // Создаем план здания
    const buildingPlan = createBuildingPlan(startX, startY, size, roomCount, rng);
    
    console.log(`Создан план здания ${size}x${size}:`);
    console.log(`- Комнат: ${buildingPlan.rooms.length}`);
    console.log(`- Внутренних соединений: ${buildingPlan.connections.length}`);
    console.log(`- Внешних дверей: ${buildingPlan.externalDoors.length}`);

    // Проверяем комнаты
    for (const room of buildingPlan.rooms) {
      console.log(`  Комната ${room.id}: (${room.x},${room.y}) ${room.width}x${room.height}`);
    }

    // Проверяем соединения
    for (const connection of buildingPlan.connections) {
      console.log(`  Соединение: комната ${connection.roomId1} <-> комната ${connection.roomId2} в (${connection.position[0]},${connection.position[1]})`);
    }

    // Проверяем внешние двери
    for (const door of buildingPlan.externalDoors) {
      console.log(`  Внешняя дверь: (${door[0]},${door[1]})`);
    }

    // Применяем план
    addBuildingDoorsFromPlan(location, buildingPlan);
    createBuildingWallsWithDoors(location, startX, startY, size, buildingPlan);

    // Проверяем результат
    const totalDoors = (location.decorations?.door?.length || 0);
    const totalWalls = (location.decorations?.wall?.length || 0);
    
    console.log(`Результат: ${totalDoors} дверей, ${totalWalls} стен`);

    // Проверяем связность (простая проверка)
    const expectedConnections = buildingPlan.rooms.length - 1; // Минимум для связности
    const actualConnections = buildingPlan.connections.length;

    if (actualConnections >= expectedConnections) {
      console.log("✓ Связность обеспечена");
    } else {
      console.log("✗ Возможна проблема со связностью");
    }

    // Проверяем однослойность стен
    checkSingleLayerWalls(location, startX, startY, size);

    // Визуализация (для небольших зданий)
    if (size <= 12) {
      console.log("\nВизуализация:");
      visualizeBuilding(location, startX, startY, size, buildingPlan);
    }

  } catch (error) {
    console.error(`Ошибка при тестировании здания ${size}x${size}:`, error);
  }
}

function visualizeBuilding(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number,
  buildingPlan: any
): void {
  const grid: string[][] = [];

  // Инициализируем сетку пустыми символами
  for (let y = 0; y < size; y++) {
    grid[y] = [];
    for (let x = 0; x < size; x++) {
      grid[y][x] = ' ';
    }
  }

  // Отмечаем стены
  const walls = location.decorations?.wall || [];
  for (const [x, y] of walls) {
    const localX = x - startX;
    const localY = y - startY;
    if (localX >= 0 && localX < size && localY >= 0 && localY < size) {
      grid[localY][localX] = '#';
    }
  }

  // Отмечаем двери
  const doors = location.decorations?.door || [];
  for (const [x, y] of doors) {
    const localX = x - startX;
    const localY = y - startY;
    if (localX >= 0 && localX < size && localY >= 0 && localY < size) {
      grid[localY][localX] = 'D';
    }
  }

  // Отмечаем комнаты номерами
  for (const room of buildingPlan.rooms) {
    const centerX = room.x - startX + Math.floor(room.width / 2);
    const centerY = room.y - startY + Math.floor(room.height / 2);
    if (centerX >= 0 && centerX < size && centerY >= 0 && centerY < size) {
      grid[centerY][centerX] = room.id.toString();
    }
  }

  // Выводим сетку
  for (let y = 0; y < size; y++) {
    console.log(grid[y].join(''));
  }

  console.log("Легенда: # = стена, D = дверь, цифры = номера комнат");
}

function checkSingleLayerWalls(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number
): void {
  const walls = location.decorations?.wall || [];
  const wallSet = new Set(walls.map(([x, y]) => `${x},${y}`));

  let doubleWallCount = 0;

  // Проверяем параллельные стены (это и есть настоящие "двойные стены")
  for (const [x, y] of walls) {
    // Проверяем горизонтальные параллельные стены (одна над другой)
    if (wallSet.has(`${x},${y + 1}`)) {
      // Проверяем, что между ними нет пустого пространства (это была бы коридор)
      // Если есть стена сверху и снизу, это может быть двойная стена
      doubleWallCount++;
    }

    // Проверяем вертикальные параллельные стены (одна рядом с другой)
    if (wallSet.has(`${x + 1},${y}`)) {
      // Аналогично для вертикальных стен
      doubleWallCount++;
    }
  }

  // Каждая пара считается дважды, поэтому делим на 2
  doubleWallCount = Math.floor(doubleWallCount / 2);

  // Но это все еще неточно. Лучше проверить по-другому:
  // Проверим, есть ли стены толщиной больше 1
  let thickWallCount = 0;

  for (const [x, y] of walls) {
    // Проверяем, есть ли стена справа И есть ли пустое место справа от неё
    if (wallSet.has(`${x + 1},${y}`) && !wallSet.has(`${x + 2},${y}`)) {
      // Это может быть стена толщиной 2
      const hasSpaceLeft = !wallSet.has(`${x - 1},${y}`);
      if (hasSpaceLeft) {
        thickWallCount++;
      }
    }

    // Аналогично для вертикальных стен
    if (wallSet.has(`${x},${y + 1}`) && !wallSet.has(`${x},${y + 2}`)) {
      const hasSpaceAbove = !wallSet.has(`${x},${y - 1}`);
      if (hasSpaceAbove) {
        thickWallCount++;
      }
    }
  }

  if (thickWallCount === 0) {
    console.log("✓ Все стены однослойные");
  } else {
    console.log(`✗ Найдено ${thickWallCount} толстых стен`);
  }
}

// Запускаем тест если файл выполняется напрямую
if (require.main === module) {
  testNewDoorSystem();
}
