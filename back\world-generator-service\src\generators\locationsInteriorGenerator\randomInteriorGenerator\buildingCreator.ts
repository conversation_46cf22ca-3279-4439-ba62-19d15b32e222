
import { TransferLocation, Point } from "../../../shared/types/Location";
import { HouseRoomSettings } from "../constants/houseRoomSettings";
import { LocationDecorations, DecorationZoneType } from "../../../shared/enums";
import { addBuildingWindows } from "./buildingCreatorComponents/windows";
import { createBuildingPlan, addBuildingDoorsFromPlan, createBuildingWallsWithDoors } from "./buildingCreatorComponents/doors";

// Интерфейс для занятого прямоугольника
export interface OccupiedArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * Создает здание на указанной позиции с заданным размером
 */
export async function createBuilding(
  location: TransferLocation,
  buildingConfig: HouseRoomSettings,
  position: Point,
  size: number,
  rng: () => number
): Promise<void> {
  const [startX, startY] = position;

  // Очищаем декорации в области здания
  await clearDecorations(location, startX, startY, size);

  // НОВАЯ СИСТЕМА: Сначала планируем всю структуру здания
  const roomCount = size >= 6 && buildingConfig.smallerRooms > 0 ? buildingConfig.smallerRooms : 1;
  const buildingPlan = createBuildingPlan(startX, startY, size, roomCount, rng);

  // Размещаем двери согласно плану
  await addBuildingDoorsFromPlan(location, buildingPlan);

  // Создаем стены с учетом запланированных дверей
  await createBuildingWallsWithDoors(location, startX, startY, size, buildingPlan);

  // Добавляем окна в конце
  await addBuildingWindows(location, startX, startY, size, rng);
}



/**
 * Проверяет и сдвигает здание если оно пересекается с другими
 */
export function adjustBuildingPosition(
  position: Point,
  size: number,
  occupiedAreas: OccupiedArea[],
  mapWidth: number,
  mapHeight: number,
  minSpacing: number = 2
): Point {
  let [x, y] = position;

  // Проверяем коллизии и сдвигаем если нужно
  for (let attempt = 0; attempt < 50; attempt++) {
    const newArea: OccupiedArea = {
      x: x - minSpacing,
      y: y - minSpacing,
      width: size + minSpacing * 2,
      height: size + minSpacing * 2
    };

    if (!checkAreaCollision(newArea, occupiedAreas) &&
        x >= 3 && y >= 3 &&
        x + size < mapWidth - 3 &&
        y + size < mapHeight - 3) {
      return [x, y];
    }

    // Сдвигаем позицию
    x += Math.random() > 0.5 ? 1 : -1;
    y += Math.random() > 0.5 ? 1 : -1;

    // Ограничиваем границами карты
    x = Math.max(3, Math.min(mapWidth - size - 3, x));
    y = Math.max(3, Math.min(mapHeight - size - 3, y));
  }

  return [x, y]; // Возвращаем последнюю позицию если не удалось найти место
}

/**
 * Очищает декорации в области здания перед установкой
 */
async function clearDecorations(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number
): Promise<void> {
  if (!location.decorations) return;

  // Проходим по всем типам декораций
  for (const decorationType in location.decorations) {
    const decorations = location.decorations[decorationType as LocationDecorations];
    if (!decorations) continue;

    // Удаляем декорации, которые попадают в область здания
    for (let i = decorations.length - 1; i >= 0; i--) {
      const [x, y] = decorations[i];

  // Проверяем, попадает ли декорация в область здания (расширенная на 1 клетку во все стороны)
  if (x >= startX - 1 && x < startX + size + 1 && y >= startY - 1 && y < startY + size + 1) {
        decorations.splice(i, 1);
      }
    }
  }

  // Также очищаем зоны декораций в этой области
  if (location.decorationZoneType) {
    for (const zoneType in location.decorationZoneType) {
      const zones = location.decorationZoneType[zoneType as DecorationZoneType];
      if (!zones) continue;

      for (let i = zones.length - 1; i >= 0; i--) {
        const [x, y] = zones[i];

  // Проверяем, попадает ли зона декораций в область здания (расширенная на 1 клетку во все стороны)
  if (x >= startX - 1 && x < startX + size + 1 && y >= startY - 1 && y < startY + size + 1) {
          zones.splice(i, 1);
        }
      }
    }
  }
}

/**
 * Создает коробку (стены) здания
 */
async function createBuildingWalls(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number
): Promise<void> {
  if (!location.decorations) {
    location.decorations = {};
  }
  
  if (!location.decorations[LocationDecorations.WALL]) {
    location.decorations[LocationDecorations.WALL] = [];
  }
  
  const walls = location.decorations[LocationDecorations.WALL];
  // Build a quick lookup to avoid duplicate wall entries
  const wallSet = new Set(walls.map(w => `${w[0]},${w[1]}`));
  
  // Верхняя и нижняя стены
  for (let x = startX; x < startX + size; x++) {
    const topKey = `${x},${startY}`;
    if (!wallSet.has(topKey)) { walls.push([x, startY]); wallSet.add(topKey); }
    const bottomKey = `${x},${startY + size - 1}`;
    if (!wallSet.has(bottomKey)) { walls.push([x, startY + size - 1]); wallSet.add(bottomKey); }
  }
  
  // Левая и правая стены
  for (let y = startY + 1; y < startY + size - 1; y++) {
    const leftKey = `${startX},${y}`;
    if (!wallSet.has(leftKey)) { walls.push([startX, y]); wallSet.add(leftKey); }
    const rightKey = `${startX + size - 1},${y}`;
    if (!wallSet.has(rightKey)) { walls.push([startX + size - 1, y]); wallSet.add(rightKey); }
  }
}



/**
 * Проверяет коллизию между областями
 */
function checkAreaCollision(
  newArea: { x: number; y: number; width: number; height: number },
  existingAreas: { x: number; y: number; width: number; height: number }[]
): boolean {
  for (const area of existingAreas) {
    if (
      newArea.x < area.x + area.width &&
      newArea.x + newArea.width > area.x &&
      newArea.y < area.y + area.height &&
      newArea.y + newArea.height > area.y
    ) {
      return true;
    }
  }
  return false;
}
