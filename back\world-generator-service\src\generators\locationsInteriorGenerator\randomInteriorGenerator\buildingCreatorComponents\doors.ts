import { TransferLocation, Point } from "../../../../shared/types/Location";
import { LocationDecorations, DecorationZoneType } from "../../../../shared/enums";

// Интерфейсы для новой системы планирования
export interface Room {
  id: number;
  x: number;
  y: number;
  width: number;
  height: number;
  type: DecorationZoneType;
}

export interface DoorConnection {
  roomId1: number;
  roomId2: number;
  position: Point;
  isExternal: boolean;
}

export interface BuildingPlan {
  rooms: Room[];
  connections: DoorConnection[];
  externalDoors: Point[];
}

/**
 * Создает план здания с комнатами и соединениями между ними
 */
export function createBuildingPlan(
  buildingStartX: number,
  buildingStartY: number,
  buildingSize: number,
  roomCount: number,
  rng: () => number
): BuildingPlan {
  // Внутренняя область здания (исключая стены)
  const innerStartX = buildingStartX + 1;
  const innerStartY = buildingStartY + 1;
  const innerSize = buildingSize - 2;

  if (innerSize < 4) {
    // Слишком маленькое здание - одна комната
    return {
      rooms: [{
        id: 1,
        x: innerStartX,
        y: innerStartY,
        width: innerSize,
        height: innerSize,
        type: DecorationZoneType.SHOP // По умолчанию
      }],
      connections: [],
      externalDoors: []
    };
  }

  // Создаем комнаты через рекурсивное деление
  const rooms = createRoomsFromSubdivision(
    innerStartX, innerStartY, innerSize, innerSize, roomCount, rng
  );

  // Планируем соединения между комнатами
  const connections = planRoomConnections(rooms, rng);

  // Планируем внешние двери
  const externalDoors = planExternalDoors(
    buildingStartX, buildingStartY, buildingSize, rng
  );

  return {
    rooms,
    connections,
    externalDoors
  };
}

/**
 * Создает комнаты через рекурсивное деление области
 */
function createRoomsFromSubdivision(
  startX: number,
  startY: number,
  width: number,
  height: number,
  targetRoomCount: number,
  rng: () => number
): Room[] {
  const areas = subdivideAreaForRooms(
    { x: startX, y: startY, width, height },
    targetRoomCount,
    rng,
    []
  );

  return areas.map((area, index) => ({
    id: index + 1,
    x: area.x,
    y: area.y,
    width: area.width,
    height: area.height,
    type: DecorationZoneType.SHOP // Пока по умолчанию, можно улучшить
  }));
}

/**
 * Рекурсивно делит область на комнаты (адаптированная версия из rooms.ts)
 */
function subdivideAreaForRooms(
  area: { x: number; y: number; width: number; height: number },
  targetRoomCount: number,
  rng: () => number,
  currentAreas: { x: number; y: number; width: number; height: number }[]
): { x: number; y: number; width: number; height: number }[] {
  // Если достигли нужного количества комнат или область слишком мала для деления
  if (currentAreas.length >= targetRoomCount ||
      (area.width < 6 && area.height < 6) ||
      (area.width < 4 || area.height < 4)) {
    return [...currentAreas, area];
  }

  // Определяем направление деления (горизонтальное или вертикальное)
  const canSplitHorizontally = area.height >= 6;
  const canSplitVertically = area.width >= 6;

  if (!canSplitHorizontally && !canSplitVertically) {
    return [...currentAreas, area];
  }

  let splitHorizontally: boolean;
  if (canSplitHorizontally && canSplitVertically) {
    // Предпочитаем делить по большей стороне
    splitHorizontally = area.height > area.width ? true : rng() > 0.5;
  } else {
    splitHorizontally = canSplitHorizontally;
  }

  if (splitHorizontally) {
    // Горизонтальное деление
    const minSplit = 3;
    const maxSplit = area.height - 3;
    const splitPoint = Math.floor(rng() * (maxSplit - minSplit + 1)) + minSplit;

    const area1 = { x: area.x, y: area.y, width: area.width, height: splitPoint };
    const area2 = { x: area.x, y: area.y + splitPoint, width: area.width, height: area.height - splitPoint };

    // Рекурсивно делим каждую часть
    const areas1 = subdivideAreaForRooms(area1, Math.ceil(targetRoomCount / 2), rng, []);
    const areas2 = subdivideAreaForRooms(area2, targetRoomCount - areas1.length, rng, []);

    return [...currentAreas, ...areas1, ...areas2];
  } else {
    // Вертикальное деление
    const minSplit = 3;
    const maxSplit = area.width - 3;
    const splitPoint = Math.floor(rng() * (maxSplit - minSplit + 1)) + minSplit;

    const area1 = { x: area.x, y: area.y, width: splitPoint, height: area.height };
    const area2 = { x: area.x + splitPoint, y: area.y, width: area.width - splitPoint, height: area.height };

    // Рекурсивно делим каждую часть
    const areas1 = subdivideAreaForRooms(area1, Math.ceil(targetRoomCount / 2), rng, []);
    const areas2 = subdivideAreaForRooms(area2, targetRoomCount - areas1.length, rng, []);

    return [...currentAreas, ...areas1, ...areas2];
  }
}

/**
 * Планирует соединения между комнатами для обеспечения связности
 */
function planRoomConnections(rooms: Room[], rng: () => number): DoorConnection[] {
  if (rooms.length <= 1) return [];

  const connections: DoorConnection[] = [];
  const connectedRooms = new Set<number>();

  // Начинаем с первой комнаты
  connectedRooms.add(rooms[0].id);

  // Подключаем все остальные комнаты к связному графу
  while (connectedRooms.size < rooms.length) {
    let bestConnection: DoorConnection | null = null;
    let shortestDistance = Infinity;

    // Ищем ближайшую несвязанную комнату к любой связанной
    for (const connectedRoomId of connectedRooms) {
      const connectedRoom = rooms.find(r => r.id === connectedRoomId)!;

      for (const room of rooms) {
        if (connectedRooms.has(room.id)) continue;

        // Проверяем, можно ли соединить эти комнаты
        const connectionPoint = findConnectionPoint(connectedRoom, room);
        if (connectionPoint) {
          const distance = Math.abs(connectedRoom.x - room.x) + Math.abs(connectedRoom.y - room.y);

          if (distance < shortestDistance) {
            shortestDistance = distance;
            bestConnection = {
              roomId1: connectedRoom.id,
              roomId2: room.id,
              position: connectionPoint,
              isExternal: false
            };
          }
        }
      }
    }

    if (bestConnection) {
      connections.push(bestConnection);
      connectedRooms.add(bestConnection.roomId2);
    } else {
      // Если не можем найти прямое соединение, прерываем
      break;
    }
  }

  return connections;
}

/**
 * Находит точку соединения между двумя комнатами (если они соседние)
 */
function findConnectionPoint(room1: Room, room2: Room): Point | null {
  // Проверяем, являются ли комнаты соседними по горизонтали
  if (room1.y < room2.y + room2.height && room1.y + room1.height > room2.y) {
    // Комнаты пересекаются по Y
    if (room1.x + room1.width === room2.x) {
      // room1 слева от room2
      const overlapStart = Math.max(room1.y, room2.y);
      const overlapEnd = Math.min(room1.y + room1.height, room2.y + room2.height);
      const doorY = Math.floor((overlapStart + overlapEnd) / 2);
      return [room1.x + room1.width - 1, doorY];
    } else if (room2.x + room2.width === room1.x) {
      // room2 слева от room1
      const overlapStart = Math.max(room1.y, room2.y);
      const overlapEnd = Math.min(room1.y + room1.height, room2.y + room2.height);
      const doorY = Math.floor((overlapStart + overlapEnd) / 2);
      return [room2.x + room2.width - 1, doorY];
    }
  }

  // Проверяем, являются ли комнаты соседними по вертикали
  if (room1.x < room2.x + room2.width && room1.x + room1.width > room2.x) {
    // Комнаты пересекаются по X
    if (room1.y + room1.height === room2.y) {
      // room1 сверху от room2
      const overlapStart = Math.max(room1.x, room2.x);
      const overlapEnd = Math.min(room1.x + room1.width, room2.x + room2.width);
      const doorX = Math.floor((overlapStart + overlapEnd) / 2);
      return [doorX, room1.y + room1.height - 1];
    } else if (room2.y + room2.height === room1.y) {
      // room2 сверху от room1
      const overlapStart = Math.max(room1.x, room2.x);
      const overlapEnd = Math.min(room1.x + room1.width, room2.x + room2.width);
      const doorX = Math.floor((overlapStart + overlapEnd) / 2);
      return [doorX, room2.y + room2.height - 1];
    }
  }

  return null;
}

/**
 * Планирует внешние двери здания
 */
function planExternalDoors(
  buildingStartX: number,
  buildingStartY: number,
  buildingSize: number,
  rng: () => number
): Point[] {
  const doors: Point[] = [];

  // Определяем возможные позиции для внешних дверей (исключая углы)
  const possiblePositions: Point[] = [];

  // Верхняя стена
  for (let x = buildingStartX + 1; x < buildingStartX + buildingSize - 1; x++) {
    possiblePositions.push([x, buildingStartY]);
  }

  // Нижняя стена
  for (let x = buildingStartX + 1; x < buildingStartX + buildingSize - 1; x++) {
    possiblePositions.push([x, buildingStartY + buildingSize - 1]);
  }

  // Левая стена
  for (let y = buildingStartY + 1; y < buildingStartY + buildingSize - 1; y++) {
    possiblePositions.push([buildingStartX, y]);
  }

  // Правая стена
  for (let y = buildingStartY + 1; y < buildingStartY + buildingSize - 1; y++) {
    possiblePositions.push([buildingStartX + buildingSize - 1, y]);
  }

  // Размещаем 1-2 внешние двери
  const doorCount = Math.min(2, Math.max(1, Math.floor(rng() * 2) + 1));

  for (let i = 0; i < doorCount && possiblePositions.length > 0; i++) {
    const doorIndex = Math.floor(rng() * possiblePositions.length);
    const doorPosition = possiblePositions.splice(doorIndex, 1)[0];
    doors.push(doorPosition);
  }

  return doors;
}

/**
 * Новая функция для размещения дверей на основе плана здания
 * Заменяет старую систему addBuildingDoors
 */
export async function addBuildingDoorsFromPlan(
  location: TransferLocation,
  buildingPlan: BuildingPlan
): Promise<void> {
  if (!location.decorations) {
    location.decorations = {};
  }

  if (!location.decorations[LocationDecorations.DOOR]) {
    location.decorations[LocationDecorations.DOOR] = [];
  }

  const doors = location.decorations[LocationDecorations.DOOR];

  // Добавляем внешние двери
  for (const externalDoor of buildingPlan.externalDoors) {
    doors.push(externalDoor);
  }

  // Добавляем внутренние двери из соединений
  for (const connection of buildingPlan.connections) {
    doors.push(connection.position);
  }

  // Устанавливаем зоны декораций для комнат
  if (!location.decorationZoneType) {
    location.decorationZoneType = {};
  }

  for (const room of buildingPlan.rooms) {
    if (!location.decorationZoneType[room.type]) {
      location.decorationZoneType[room.type] = [];
    }

    // Добавляем все внутренние клетки комнаты в зону декораций
    for (let x = room.x + 1; x < room.x + room.width - 1; x++) {
      for (let y = room.y + 1; y < room.y + room.height - 1; y++) {
        location.decorationZoneType[room.type].push([x, y, room.id]);
      }
    }
  }
}

/**
 * Создает стены здания с учетом запланированных дверей
 */
export async function createBuildingWallsWithDoors(
  location: TransferLocation,
  buildingStartX: number,
  buildingStartY: number,
  buildingSize: number,
  buildingPlan: BuildingPlan
): Promise<void> {
  if (!location.decorations) {
    location.decorations = {};
  }

  if (!location.decorations[LocationDecorations.WALL]) {
    location.decorations[LocationDecorations.WALL] = [];
  }

  const walls = location.decorations[LocationDecorations.WALL];
  const wallSet = new Set(walls.map(w => `${w[0]},${w[1]}`));

  // Создаем множество позиций дверей для быстрого поиска
  const doorPositions = new Set<string>();
  for (const door of buildingPlan.externalDoors) {
    doorPositions.add(`${door[0]},${door[1]}`);
  }
  for (const connection of buildingPlan.connections) {
    doorPositions.add(`${connection.position[0]},${connection.position[1]}`);
  }

  // Создаем внешние стены здания (исключая позиции дверей)
  // Верхняя и нижняя стены
  for (let x = buildingStartX; x < buildingStartX + buildingSize; x++) {
    const topKey = `${x},${buildingStartY}`;
    if (!wallSet.has(topKey) && !doorPositions.has(topKey)) {
      walls.push([x, buildingStartY]);
      wallSet.add(topKey);
    }

    const bottomKey = `${x},${buildingStartY + buildingSize - 1}`;
    if (!wallSet.has(bottomKey) && !doorPositions.has(bottomKey)) {
      walls.push([x, buildingStartY + buildingSize - 1]);
      wallSet.add(bottomKey);
    }
  }

  // Левая и правая стены
  for (let y = buildingStartY; y < buildingStartY + buildingSize; y++) {
    const leftKey = `${buildingStartX},${y}`;
    if (!wallSet.has(leftKey) && !doorPositions.has(leftKey)) {
      walls.push([buildingStartX, y]);
      wallSet.add(leftKey);
    }

    const rightKey = `${buildingStartX + buildingSize - 1},${y}`;
    if (!wallSet.has(rightKey) && !doorPositions.has(rightKey)) {
      walls.push([buildingStartX + buildingSize - 1, y]);
      wallSet.add(rightKey);
    }
  }

  // Создаем внутренние стены между комнатами (исключая позиции дверей)
  createInternalWallsFromRooms(buildingPlan.rooms, walls, wallSet, doorPositions);
}

/**
 * Создает внутренние стены между комнатами
 */
function createInternalWallsFromRooms(
  rooms: Room[],
  walls: Point[],
  wallSet: Set<string>,
  doorPositions: Set<string>
): void {
  // Создаем стены по периметру каждой комнаты
  for (const room of rooms) {
    // Верхняя стена комнаты
    for (let x = room.x; x < room.x + room.width; x++) {
      const wallKey = `${x},${room.y}`;
      if (!wallSet.has(wallKey) && !doorPositions.has(wallKey)) {
        walls.push([x, room.y]);
        wallSet.add(wallKey);
      }
    }

    // Нижняя стена комнаты
    for (let x = room.x; x < room.x + room.width; x++) {
      const wallKey = `${x},${room.y + room.height - 1}`;
      if (!wallSet.has(wallKey) && !doorPositions.has(wallKey)) {
        walls.push([x, room.y + room.height - 1]);
        wallSet.add(wallKey);
      }
    }

    // Левая стена комнаты
    for (let y = room.y; y < room.y + room.height; y++) {
      const wallKey = `${room.x},${y}`;
      if (!wallSet.has(wallKey) && !doorPositions.has(wallKey)) {
        walls.push([room.x, y]);
        wallSet.add(wallKey);
      }
    }

    // Правая стена комнаты
    for (let y = room.y; y < room.y + room.height; y++) {
      const wallKey = `${room.x + room.width - 1},${y}`;
      if (!wallSet.has(wallKey) && !doorPositions.has(wallKey)) {
        walls.push([room.x + room.width - 1, y]);
        wallSet.add(wallKey);
      }
    }
  }
}

/**
 * Добавляет двери в здание
 */
export async function addBuildingDoors(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number,
  doorCount: number,
  rng: () => number
): Promise<void> {
  if (!location.decorations) {
    location.decorations = {};
  }

  if (!location.decorations[LocationDecorations.DOOR]) {
    location.decorations[LocationDecorations.DOOR] = [];
  }

  const doors = location.decorations[LocationDecorations.DOOR];
  const walls = location.decorations[LocationDecorations.WALL] || [];

  // Собираем все возможные позиции для ВНЕШНИХ дверей (только внешние стены здания)
  const possibleExternalDoorPositions: Point[] = [];

  // Проверяем каждую стену здания
  for (const wall of walls) {
    const [x, y] = wall;

    // Проверяем, что это внешняя стена здания
    const isExternalWall = (x === startX || x === startX + size - 1 ||
                           y === startY || y === startY + size - 1);

    if (!isExternalWall) continue;

    // Исключаем углы
    const isCorner = (x === startX || x === startX + size - 1) &&
                     (y === startY || y === startY + size - 1);

    if (isCorner) continue;

    // Проверяем, что это не стык трех стен (внутренний угол)
    const isJunction = isThreeWallJunction(x, y, walls);

    if (isJunction) continue;

    possibleExternalDoorPositions.push([x, y]);
  }

  // Размещаем ВНЕШНИЕ двери (входы/выходы в здание)
  for (let i = 0; i < Math.min(doorCount, possibleExternalDoorPositions.length); i++) {
    const doorIndex = Math.floor(rng() * possibleExternalDoorPositions.length);
    const doorPosition = possibleExternalDoorPositions.splice(doorIndex, 1)[0];

    doors.push(doorPosition);

    // Удаляем стену в позиции двери
    const wallIndex = walls.findIndex(wall => wall[0] === doorPosition[0] && wall[1] === doorPosition[1]);
    if (wallIndex !== -1) {
      walls.splice(wallIndex, 1);
    }
  }

  // Добавляем ВНУТРЕННИЕ двери между комнатами
  await addInternalDoors(location, startX, startY, size, rng);
}

/**
 * Добавляет внутренние двери между комнатами
 */
export async function addInternalDoors(
  location: TransferLocation,
  buildingStartX: number,
  buildingStartY: number,
  buildingSize: number,
  rng: () => number
): Promise<void> {
  const walls = location.decorations?.[LocationDecorations.WALL] || [];
  const doors = location.decorations?.[LocationDecorations.DOOR] || [];

  // Находим все комнаты по зонам декораций
  const rooms = findRoomsFromDecorationZones(location);

  if (rooms.length <= 1) return; // Нет смысла добавлять двери если комната одна или их нет

  // Определяем главный выход из здания (ближайший к центру карты)
  const buildingCenterX = buildingStartX + buildingSize / 2;
  const buildingCenterY = buildingStartY + buildingSize / 2;

  let mainExit: Point | null = null;
  let minDistanceToCenter = Infinity;

  for (const door of doors) {
    const [x, y] = door;
    // Проверяем, что это внешняя дверь
    const isExternalDoor = (x === buildingStartX || x === buildingStartX + buildingSize - 1 ||
                           y === buildingStartY || y === buildingStartY + buildingSize - 1);

    if (isExternalDoor) {
      const distance = Math.sqrt(Math.pow(x - buildingCenterX, 2) + Math.pow(y - buildingCenterY, 2));
      if (distance < minDistanceToCenter) {
        minDistanceToCenter = distance;
        mainExit = [x, y];
      }
    }
  }

  // Для каждой комнаты добавляем одну дверь, направленную к выходу
  const usedWalls = new Set<string>(); // Отслеживаем использованные стены

  for (const room of rooms) {
    // Находим стены комнаты
    const roomWalls = findRoomWalls(room, walls, buildingStartX, buildingStartY, buildingSize);

    if (roomWalls.length === 0) continue;

    // Определяем направление к выходу
    const roomCenterX = room.x + room.width / 2;
    const roomCenterY = room.y + room.height / 2;

    let targetDirection: 'north' | 'south' | 'east' | 'west' = 'south';

    if (mainExit) {
      const [exitX, exitY] = mainExit;
      const deltaX = exitX - roomCenterX;
      const deltaY = exitY - roomCenterY;

      // Определяем основное направление к выходу
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        targetDirection = deltaX > 0 ? 'east' : 'west';
      } else {
        targetDirection = deltaY > 0 ? 'south' : 'north';
      }
    }

    // Группируем стены по направлениям
    const wallsByDirection = {
      north: roomWalls.filter(([x, y]) => y === room.y),
      south: roomWalls.filter(([x, y]) => y === room.y + room.height - 1),
      west: roomWalls.filter(([x, y]) => x === room.x),
      east: roomWalls.filter(([x, y]) => x === room.x + room.width - 1)
    };

    // Пытаемся разместить дверь в предпочтительном направлении
    const directions = [targetDirection, 'north', 'south', 'east', 'west'];

    for (const direction of directions) {
      // Если на отрезке стены комнаты уже есть дверь, пропускаем направление
      const existingDoorOnSide = doors.some(([dx, dy]) => {
        if (direction === 'north') {
          return dy === room.y && dx >= room.x && dx < room.x + room.width;
        } else if (direction === 'south') {
          return dy === room.y + room.height - 1 && dx >= room.x && dx < room.x + room.width;
        } else if (direction === 'west') {
          return dx === room.x && dy >= room.y && dy < room.y + room.height;
        } else { // east
          return dx === room.x + room.width - 1 && dy >= room.y && dy < room.y + room.height;
        }
      });

      if (existingDoorOnSide) continue;

      const availableWalls = wallsByDirection[direction].filter(wall => {
        const wallKey = `${wall[0]},${wall[1]}`;
        return !usedWalls.has(wallKey);
      });

      if (availableWalls.length > 0) {
        // Выбираем случайную стену из доступных
        const wallIndex = Math.floor(rng() * availableWalls.length);
        const doorPosition = availableWalls[wallIndex];

        doors.push(doorPosition);

        // Удаляем стену в позиции двери
        const wallArrayIndex = walls.findIndex(wall =>
          wall[0] === doorPosition[0] && wall[1] === doorPosition[1]
        );
        if (wallArrayIndex !== -1) {
          walls.splice(wallArrayIndex, 1);
        }

        // Отмечаем стену как использованную
        usedWalls.add(`${doorPosition[0]},${doorPosition[1]}`);
        break;
      }
    }
  }
}

/**
 * Проверяет, является ли позиция стыком трех стен
 */
export function isThreeWallJunction(x: number, y: number, walls: Point[]): boolean {
  // Проверяем соседние позиции
  const neighbors = [
    [x - 1, y], [x + 1, y], // горизонтальные соседи
    [x, y - 1], [x, y + 1]  // вертикальные соседи
  ];

  let wallCount = 0;
  for (const [nx, ny] of neighbors) {
    const hasWall = walls.some(wall => wall[0] === nx && wall[1] === ny);
    if (hasWall) wallCount++;
  }

  // Если у позиции 3 или более соседних стен, это стык
  return wallCount >= 3;
}

/**
 * Находит комнаты из зон декораций
 */
function findRoomsFromDecorationZones(location: TransferLocation): Array<{x: number, y: number, width: number, height: number}> {
  const rooms: Array<{x: number, y: number, width: number, height: number}> = [];

  if (!location.decorationZoneType) return rooms;

  // Группируем зоны по ID
  const zoneGroups = new Map<number, Point[]>();

  for (const zoneType in location.decorationZoneType) {
    const zones = location.decorationZoneType[zoneType as DecorationZoneType];
    if (!zones) continue;

    for (const zone of zones) {
      const [x, y, zoneId] = zone;
      if (!zoneGroups.has(zoneId)) {
        zoneGroups.set(zoneId, []);
      }
      zoneGroups.get(zoneId)!.push([x, y]);
    }
  }

  // Для каждой группы зон определяем границы комнаты
  for (const [zoneId, points] of zoneGroups) {
    if (points.length === 0) continue;

    let minX = points[0][0], maxX = points[0][0];
    let minY = points[0][1], maxY = points[0][1];

    for (const [x, y] of points) {
      minX = Math.min(minX, x);
      maxX = Math.max(maxX, x);
      minY = Math.min(minY, y);
      maxY = Math.max(maxY, y);
    }

    // Расширяем границы на 1 клетку для учета стен
    rooms.push({
      x: minX - 1,
      y: minY - 1,
      width: maxX - minX + 3,
      height: maxY - minY + 3
    });
  }

  return rooms;
}

/**
 * Находит стены комнаты (только внутренние стены, не внешние стены здания)
 */
function findRoomWalls(
  room: {x: number, y: number, width: number, height: number},
  allWalls: Point[],
  buildingStartX: number,
  buildingStartY: number,
  buildingSize: number
): Point[] {
  const roomWalls: Point[] = [];

  for (const wall of allWalls) {
    const [x, y] = wall;

    // Проверяем, что стена принадлежит периметру комнаты
    const isRoomPerimeter = (
      (x === room.x || x === room.x + room.width - 1) && y >= room.y && y < room.y + room.height
    ) || (
      (y === room.y || y === room.y + room.height - 1) && x >= room.x && x < room.x + room.width
    );

    if (!isRoomPerimeter) continue;

    // Исключаем внешние стены здания
    const isBuildingExternalWall = (
      x === buildingStartX || x === buildingStartX + buildingSize - 1 ||
      y === buildingStartY || y === buildingStartY + buildingSize - 1
    );

    if (isBuildingExternalWall) continue;

    // Исключаем углы комнаты
    const isRoomCorner = (
      (x === room.x || x === room.x + room.width - 1) &&
      (y === room.y || y === room.y + room.height - 1)
    );

    if (isRoomCorner) continue;

    roomWalls.push([x, y]);
  }

  return roomWalls;
}